import { http } from "viem";
import { privateKeyToAccount, type Account, type Address } from "viem/accounts";
import { StoryClient, type StoryConfig, type LicenseTerms, type LicensingConfig } from "@story-protocol/core-sdk";
import { createHash } from "crypto";
import { parseEther, zeroAddress } from "viem";
import type { IpMetadata } from "@story-protocol/core-sdk";
import { PinataSDK } from "pinata-web3";

const privateKey: Address = `0x${process.env.WALLET_PRIVATE_KEY}`;
const account: Account = privateKeyToAccount(privateKey);

const config: StoryConfig = {
  account: account,
  transport: http(process.env.RPC_PROVIDER_URL),
  chainId: "aeneid",
};

export const client = StoryClient.newClient(config);

const pinata = new PinataSDK({
  pinataJwt: process.env.PINATA_JWT,
});

export async function uploadJSONToIPFS(jsonMetadata: any): Promise<string> {
  const { IpfsHash } = await pinata.upload.json(jsonMetadata);
  return IpfsHash;
}

// 定义不同类型的单张形象授权许可证条款
export const SingleImageLicenseTypes = {
  // 1. 个人使用许可证 - 免费，仅限个人非商业使用
  PERSONAL_USE: {
    transferable: false,
    royaltyPolicy: "******************************************", // RoyaltyPolicyLAP
    defaultMintingFee: 0n,
    expiration: 0n,
    commercialUse: false,
    commercialAttribution: true,
    commercializerChecker: zeroAddress,
    commercializerCheckerData: "0x",
    commercialRevShare: 0,
    commercialRevCeiling: 0n,
    derivativesAllowed: false,
    derivativesAttribution: true,
    derivativesApproval: false,
    derivativesReciprocal: false,
    derivativeRevCeiling: 0n,
    currency: "******************************************", // $WIP
    uri: "https://github.com/piplabs/pil-document/blob/main/off-chain-terms/PersonalUse.json"
  } as LicenseTerms,

  // 2. 商业使用许可证 - 付费，允许商业使用
  COMMERCIAL_USE: {
    transferable: true,
    royaltyPolicy: "******************************************",
    defaultMintingFee: parseEther("10"), // 10 $WIP
    expiration: 0n,
    commercialUse: true,
    commercialAttribution: true,
    commercializerChecker: zeroAddress,
    commercializerCheckerData: "0x",
    commercialRevShare: 15, // 15% 收益分成
    commercialRevCeiling: 0n,
    derivativesAllowed: false,
    derivativesAttribution: true,
    derivativesApproval: false,
    derivativesReciprocal: false,
    derivativeRevCeiling: 0n,
    currency: "******************************************",
    uri: "https://github.com/piplabs/pil-document/blob/main/off-chain-terms/CommercialUse.json"
  } as LicenseTerms,

  // 3. 衍生作品许可证 - 允许创建衍生作品
  DERIVATIVE_WORKS: {
    transferable: true,
    royaltyPolicy: "******************************************",
    defaultMintingFee: parseEther("5"), // 5 $WIP
    expiration: 0n,
    commercialUse: true,
    commercialAttribution: true,
    commercializerChecker: zeroAddress,
    commercializerCheckerData: "0x",
    commercialRevShare: 25, // 25% 收益分成
    commercialRevCeiling: 0n,
    derivativesAllowed: true,
    derivativesAttribution: true,
    derivativesApproval: false,
    derivativesReciprocal: true, // 衍生作品必须使用相同许可证
    derivativeRevCeiling: 0n,
    currency: "******************************************",
    uri: "https://github.com/piplabs/pil-document/blob/main/off-chain-terms/DerivativeWorks.json"
  } as LicenseTerms,

  // 4. 限量商业许可证 - 限制许可证数量的商业使用
  LIMITED_COMMERCIAL: {
    transferable: false,
    royaltyPolicy: "******************************************",
    defaultMintingFee: parseEther("50"), // 50 $WIP 高价格
    expiration: BigInt(365 * 24 * 60 * 60), // 1年有效期
    commercialUse: true,
    commercialAttribution: true,
    commercializerChecker: zeroAddress,
    commercializerCheckerData: "0x",
    commercialRevShare: 30, // 30% 收益分成
    commercialRevCeiling: parseEther("1000"), // 最高收益上限
    derivativesAllowed: false,
    derivativesAttribution: true,
    derivativesApproval: true, // 需要批准
    derivativesReciprocal: false,
    derivativeRevCeiling: 0n,
    currency: "******************************************",
    uri: "https://github.com/piplabs/pil-document/blob/main/off-chain-terms/LimitedCommercial.json"
  } as LicenseTerms
};

// 为不同许可证类型配置授权配置
export const LicensingConfigurations = {
  PERSONAL_USE: {
    isSet: true,
    mintingFee: 0n,
    licensingHook: zeroAddress,
    hookData: "0x",
    commercialRevShare: 0,
    disabled: false,
    expectMinimumGroupRewardShare: 0,
    expectGroupRewardPool: zeroAddress
  } as LicensingConfig,

  COMMERCIAL_USE: {
    isSet: true,
    mintingFee: parseEther("10"),
    licensingHook: zeroAddress,
    hookData: "0x",
    commercialRevShare: 15,
    disabled: false,
    expectMinimumGroupRewardShare: 0,
    expectGroupRewardPool: zeroAddress
  } as LicensingConfig,

  DERIVATIVE_WORKS: {
    isSet: true,
    mintingFee: parseEther("5"),
    licensingHook: zeroAddress,
    hookData: "0x",
    commercialRevShare: 25,
    disabled: false,
    expectMinimumGroupRewardShare: 0,
    expectGroupRewardPool: zeroAddress
  } as LicensingConfig,

  LIMITED_COMMERCIAL: {
    isSet: true,
    mintingFee: parseEther("50"),
    licensingHook: "******************************************", // TotalLicenseTokenLimitHook
    hookData: "0x",
    commercialRevShare: 30,
    disabled: false,
    expectMinimumGroupRewardShare: 0,
    expectGroupRewardPool: zeroAddress
  } as LicensingConfig
};

// 单张形象授权管理类
export class SingleImageLicenseManager {
  private client: StoryClient;
  private registeredLicenseTermsIds: Map<string, bigint> = new Map();

  constructor(storyClient: StoryClient) {
    this.client = storyClient;
  }

  // 注册所有许可证条款类型
  async registerAllLicenseTerms(): Promise<Map<string, bigint>> {
    console.log("开始注册单张形象授权许可证条款...");

    for (const [licenseType, terms] of Object.entries(SingleImageLicenseTypes)) {
      try {
        console.log(`注册 ${licenseType} 许可证条款...`);
        
        const response = await this.client.license.registerPILTerms({
          ...terms,
          txOptions: { waitForTransaction: true }
        });

        if (response.licenseTermsId) {
          this.registeredLicenseTermsIds.set(licenseType, response.licenseTermsId);
          console.log(`✅ ${licenseType} 许可证条款已注册，ID: ${response.licenseTermsId}`);
          console.log(`   交易哈希: ${response.txHash}`);
        }
      } catch (error) {
        console.error(`❌ 注册 ${licenseType} 许可证条款失败:`, error);
      }
    }

    return this.registeredLicenseTermsIds;
  }

  // 为单张图像创建带有多种授权选项的 IP 资产
  async createSingleImageIPWithLicensing(
    imageMetadata: {
      title: string;
      description: string;
      imageUrl: string;
    },
    spgNftContract: Address,
    licenseTypes: string[] = ["PERSONAL_USE", "COMMERCIAL_USE"]
  ) {
    console.log(`开始为图像 "${imageMetadata.title}" 创建 IP 资产...`);

    // 生成 IP 元数据
    const ipMetadata: IpMetadata = this.client.ipAsset.generateIpMetadata({
      title: imageMetadata.title,
      description: imageMetadata.description,
      watermarkImg: imageMetadata.imageUrl,
    });

    // 生成 NFT 元数据
    const nftMetadata = {
      name: imageMetadata.title,
      description: imageMetadata.description,
      image: imageMetadata.imageUrl,
    };

    // 上传到 IPFS
    const ipIpfsHash = await uploadJSONToIPFS(ipMetadata);
    const ipHash = createHash("sha256")
      .update(JSON.stringify(ipMetadata))
      .digest("hex");

    const nftIpfsHash = await uploadJSONToIPFS(nftMetadata);
    const nftHash = createHash("sha256")
      .update(JSON.stringify(nftMetadata))
      .digest("hex");

    // 准备许可证条款数据
    const licenseTermsData = licenseTypes.map(licenseType => {
      const licenseTermsId = this.registeredLicenseTermsIds.get(licenseType);
      if (!licenseTermsId) {
        throw new Error(`许可证类型 ${licenseType} 尚未注册`);
      }

      return {
        terms: SingleImageLicenseTypes[licenseType as keyof typeof SingleImageLicenseTypes],
        licensingConfig: LicensingConfigurations[licenseType as keyof typeof LicensingConfigurations]
      };
    });

    // 创建 IP 资产并附加许可证条款
    const response = await this.client.ipAsset.mintAndRegisterIpAssetWithPilTerms({
      spgNftContract,
      licenseTermsData,
      ipMetadata: {
        ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash}`,
        ipMetadataHash: `0x${ipHash}`,
        nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash}`,
        nftMetadataHash: `0x${nftHash}`,
      },
      txOptions: { waitForTransaction: true },
    });

    console.log(`✅ 单张图像 IP 资产创建成功!`);
    console.log(`   IP ID: ${response.ipId}`);
    console.log(`   Token ID: ${response.tokenId}`);
    console.log(`   交易哈希: ${response.txHash}`);
    console.log(`   许可证条款 IDs: ${response.licenseTermsIds}`);
    console.log(`   浏览器查看: https://aeneid.explorer.story.foundation/ipa/${response.ipId}`);

    return response;
  }

  // 为现有 IP 资产添加新的许可证条款
  async attachLicenseToExistingIP(ipId: Address, licenseType: string) {
    const licenseTermsId = this.registeredLicenseTermsIds.get(licenseType);
    if (!licenseTermsId) {
      throw new Error(`许可证类型 ${licenseType} 尚未注册`);
    }

    console.log(`为 IP ${ipId} 附加 ${licenseType} 许可证...`);

    const response = await this.client.license.attachLicenseTerms({
      licenseTermsId: licenseTermsId.toString(),
      ipId,
      txOptions: { waitForTransaction: true },
    });

    if (response.success) {
      console.log(`✅ 许可证条款已成功附加到 IP 资产`);
      console.log(`   交易哈希: ${response.txHash}`);
    } else {
      console.log(`ℹ️ 许可证条款已经附加到此 IP 资产`);
    }

    return response;
  }

  // 铸造许可证代币
  async mintLicenseToken(
    licensorIpId: Address,
    licenseType: string,
    amount: number = 1,
    receiver?: Address
  ) {
    const licenseTermsId = this.registeredLicenseTermsIds.get(licenseType);
    if (!licenseTermsId) {
      throw new Error(`许可证类型 ${licenseType} 尚未注册`);
    }

    console.log(`为 IP ${licensorIpId} 铸造 ${amount} 个 ${licenseType} 许可证代币...`);

    const response = await this.client.license.mintLicenseTokens({
      licenseTermsId: licenseTermsId.toString(),
      licensorIpId,
      amount,
      receiver,
      maxMintingFee: SingleImageLicenseTypes[licenseType as keyof typeof SingleImageLicenseTypes].defaultMintingFee,
      maxRevenueShare: SingleImageLicenseTypes[licenseType as keyof typeof SingleImageLicenseTypes].commercialRevShare,
      txOptions: { waitForTransaction: true },
    });

    console.log(`✅ 许可证代币铸造成功!`);
    console.log(`   许可证代币 IDs: ${response.licenseTokenIds}`);
    console.log(`   交易哈希: ${response.txHash}`);

    return response;
  }

  // 获取已注册的许可证条款 ID
  getLicenseTermsId(licenseType: string): bigint | undefined {
    return this.registeredLicenseTermsIds.get(licenseType);
  }

  // 列出所有已注册的许可证条款
  listRegisteredLicenseTerms(): Map<string, bigint> {
    return new Map(this.registeredLicenseTermsIds);
  }
}
