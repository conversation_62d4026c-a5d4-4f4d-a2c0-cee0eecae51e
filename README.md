# Story Protocol Demo

这是一个基于 Story Protocol 的 IP 资产管理演示项目，包含了基础的 IP 注册功能和新增的单张形象授权功能。

## 功能特性

### 🎨 基础功能
- IP 资产注册和管理
- NFT 集合创建
- 元数据上传到 IPFS

### 🔐 单张形象授权功能 (新增)
- 四种不同类型的许可证：个人使用、商业使用、衍生作品、限量商业
- 碎片化授权管理
- 许可证代币铸造
- 灵活的授权配置

## 安装依赖

```bash
bun install
```

## 环境配置

复制环境变量示例文件并配置：

```bash
cp .env.example .env
# 编辑 .env 文件，填入你的配置
```

## 运行项目

### 基础演示
```bash
# 运行基础 IP 注册演示
bun run dev

# 运行主要演示
bun run main
```

### 单张形象授权功能
```bash
# 运行单张形象授权演示
bun run single-image-demo

# 运行功能测试
bun run test-single-image
```

## 项目结构

```
├── index.ts                     # 基础 IP 注册演示
├── index_main.ts               # 主要演示文件
├── single-image-licensing.ts   # 单张形象授权核心功能
├── single-image-demo.ts        # 单张形象授权演示
├── test-single-image-licensing.ts # 功能测试
├── SINGLE_IMAGE_LICENSING.md   # 单张形象授权详细文档
└── README.md                   # 本文件
```

## 详细文档

- [单张形象授权功能详细文档](./SINGLE_IMAGE_LICENSING.md)

## 技术栈

- [Bun](https://bun.sh) - JavaScript 运行时
- [Story Protocol](https://story.foundation) - IP 协议
- [Viem](https://viem.sh) - 以太坊客户端
- [Pinata](https://pinata.cloud) - IPFS 服务

This project was created using `bun init` in bun v1.1.38.
