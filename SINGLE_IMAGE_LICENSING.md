# 单张形象授权功能文档

## 概述

本项目为在 Story Protocol 上部署的 IP 增加了「碎片化授权」里的【单张形象授权】功能。该功能允许创作者为每张图像设置不同类型的许可证，实现精细化的授权管理。

## 功能特性

### 🎯 四种许可证类型

1. **个人使用许可证 (PERSONAL_USE)**
   - 免费获取
   - 仅限个人非商业使用
   - 不可转让
   - 需要署名

2. **商业使用许可证 (COMMERCIAL_USE)**
   - 付费获取 (10 $WIP)
   - 允许商业使用
   - 可转让
   - 15% 收益分成

3. **衍生作品许可证 (DERIVATIVE_WORKS)**
   - 付费获取 (5 $WIP)
   - 允许创建衍生作品
   - 衍生作品必须使用相同许可证
   - 25% 收益分成

4. **限量商业许可证 (LIMITED_COMMERCIAL)**
   - 高价获取 (50 $WIP)
   - 限时有效 (1年)
   - 收益上限 (1000 $WIP)
   - 需要批准衍生作品
   - 30% 收益分成

### 🔧 核心功能

- **批量许可证注册**: 一次性注册所有许可证类型
- **灵活的 IP 创建**: 为每张图像选择适合的许可证组合
- **动态许可证附加**: 为现有 IP 添加新的许可证类型
- **许可证代币铸造**: 用户可以购买特定类型的许可证
- **授权配置管理**: 精细化控制每种许可证的参数

## 文件结构

```
├── single-image-licensing.ts    # 核心功能实现
├── single-image-demo.ts         # 功能演示
├── test-single-image-licensing.ts # 功能测试
└── SINGLE_IMAGE_LICENSING.md    # 本文档
```

## 快速开始

### 1. 环境准备

确保你有以下环境变量：

```bash
WALLET_PRIVATE_KEY=your_private_key_without_0x
RPC_PROVIDER_URL=https://aeneid.storyrpc.io/
PINATA_JWT=your_pinata_jwt_token
```

### 2. 安装依赖

```bash
bun install
```

### 3. 运行演示

```bash
# 运行完整功能演示
bun run single-image-demo

# 运行功能测试
bun run test-single-image
```

## 使用指南

### 基本使用

```typescript
import { client } from "./single-image-licensing";
import { SingleImageLicenseManager } from "./single-image-licensing";

// 创建许可证管理器
const licenseManager = new SingleImageLicenseManager(client);

// 注册所有许可证条款
await licenseManager.registerAllLicenseTerms();

// 创建带有多种授权的图像 IP
const response = await licenseManager.createSingleImageIPWithLicensing(
  {
    title: "我的艺术作品",
    description: "这是一个支持多种授权的艺术作品",
    imageUrl: "https://example.com/my-image.jpg"
  },
  spgNftContract,
  ["PERSONAL_USE", "COMMERCIAL_USE", "DERIVATIVE_WORKS"]
);
```

### 高级功能

```typescript
// 为现有 IP 添加新的许可证类型
await licenseManager.attachLicenseToExistingIP(
  ipId,
  "LIMITED_COMMERCIAL"
);

// 铸造许可证代币
await licenseManager.mintLicenseToken(
  licensorIpId,
  "COMMERCIAL_USE",
  1, // 数量
  receiverAddress
);

// 查询许可证条款 ID
const licenseTermsId = licenseManager.getLicenseTermsId("PERSONAL_USE");
```

## 许可证条款详细配置

### 个人使用许可证
- **费用**: 免费
- **用途**: 个人非商业使用
- **转让**: 不可转让
- **署名**: 必须
- **衍生**: 不允许

### 商业使用许可证
- **费用**: 10 $WIP
- **用途**: 商业使用
- **转让**: 可转让
- **署名**: 必须
- **收益分成**: 15%
- **衍生**: 不允许

### 衍生作品许可证
- **费用**: 5 $WIP
- **用途**: 商业使用 + 衍生作品
- **转让**: 可转让
- **署名**: 必须
- **收益分成**: 25%
- **衍生**: 允许，必须使用相同许可证

### 限量商业许可证
- **费用**: 50 $WIP
- **用途**: 高级商业使用
- **转让**: 不可转让
- **有效期**: 1年
- **收益上限**: 1000 $WIP
- **收益分成**: 30%
- **衍生**: 需要批准

## 测试说明

运行测试以验证功能：

```bash
bun run test-single-image
```

测试包括：
1. 许可证条款配置验证
2. 许可证条款注册
3. NFT 集合创建
4. 单张图像 IP 资产创建
5. 许可证条款附加
6. 许可证代币铸造
7. 许可证条款查询

## 注意事项

1. **余额要求**: 确保钱包有足够的 $WIP 代币用于支付交易费用和许可证费用
2. **网络配置**: 当前配置为 Aeneid 测试网，生产环境需要修改 chainId
3. **IPFS 配置**: 需要有效的 Pinata JWT 令牌用于元数据上传
4. **许可证费用**: 不同许可证类型有不同的铸造费用，用户需要支付相应费用

## 错误处理

常见错误及解决方案：

1. **余额不足**: 确保钱包有足够的 $WIP 代币
2. **网络连接**: 检查 RPC 提供商 URL 是否正确
3. **IPFS 上传失败**: 验证 Pinata JWT 令牌是否有效
4. **许可证条款未注册**: 先运行 `registerAllLicenseTerms()` 方法

## 扩展功能

可以根据需要扩展更多许可证类型：

```typescript
// 添加新的许可证类型
const CUSTOM_LICENSE: LicenseTerms = {
  // 自定义配置
};

// 添加到 SingleImageLicenseTypes 对象中
```

## 支持

如有问题，请查看：
1. Story Protocol 官方文档
2. 项目测试文件中的示例
3. 控制台输出的详细错误信息
