import { http } from "viem";
import { privateKeyToAccount, type Account, type Address } from "viem/accounts";
import { StoryClient, type StoryConfig } from "@story-protocol/core-sdk";
import { createHash } from "crypto";
import type { IpMetadata } from "@story-protocol/core-sdk";
import { zeroAddress } from "viem";

const privateKey: Address = `0x${process.env.WALLET_PRIVATE_KEY}`;
const account: Account = privateKeyToAccount(privateKey);

const config: StoryConfig = {
  account: account, // the account object from above
  transport: http(process.env.RPC_PROVIDER_URL),
  chainId: "aeneid",
};
export const client = StoryClient.newClient(config);

import { PinataSDK } from "pinata-web3";

const pinata = new PinataSDK({
  pinataJwt: process.env.PINATA_JWT,
});

export async function uploadJSONToIPFS(jsonMetadata: any): Promise<string> {
  const { IpfsHash } = await pinata.upload.json(jsonMetadata);
  return IpfsHash;
}

async function main() {
  const ipMetadata: IpMetadata = client.ipAsset.generateIpMetadata({
    title: "TestNFT One",
    description: "This is an test NFT\nno1 representing owernship of our IP Asset.",
    watermarkImg: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeiev6gatkl5oqtietqqpg5iocpzcqppwnjnipgwwuas2udxpzo7gxy",
  });

  const nftMetadata = {
    name: "TestNFT One",
    description: "This is an test NFT\nno1 representing owernship of our IP Asset.",
    image: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeiev6gatkl5oqtietqqpg5iocpzcqppwnjnipgwwuas2udxpzo7gxy",
  };


  const ipMetadata2: IpMetadata = client.ipAsset.generateIpMetadata({
    title: "TestNFT Two",
    description: "his is an test NFT no2 representing owernship of our IP Asset.",
    watermarkImg: "https://picsum.photos/id/237/200/300",
  });

  const nftMetadata2 = {
    name: "TestNFT Two",
    description: "This is an test NFT no2 representing owernship of our IP Asset.",
    image: "https://picsum.photos/id/237/200/300",
  };

  const ipMetadata3: IpMetadata = client.ipAsset.generateIpMetadata({
    title: "TestNFT Three",
    description: "This is an test NFT no3 representing owernship of our IP Asset.",
    watermarkImg: "https://picsum.photos/id/238/200/300",
  });

  const nftMetadata3 = {
    name: "TestNFT Three",
    description: "This is an test NFT no3 representing owernship of our IP Asset.",
    image: "https://picsum.photos/id/238/200/300",
  };

  const ipIpfsHash = await uploadJSONToIPFS(ipMetadata);
  const ipHash = createHash("sha256")
    .update(JSON.stringify(ipMetadata))
    .digest("hex");

  const ipIpfsHash2 = await uploadJSONToIPFS(ipMetadata2);
  const ipHash2 = createHash("sha256")
    .update(JSON.stringify(ipMetadata2))
    .digest("hex");

  const ipIpfsHash3 = await uploadJSONToIPFS(ipMetadata3);
  const ipHash3 = createHash("sha256")
    .update(JSON.stringify(ipMetadata3))
    .digest("hex");

  const nftIpfsHash = await uploadJSONToIPFS(nftMetadata);
  const nftHash = createHash("sha256")
    .update(JSON.stringify(nftMetadata))
    .digest("hex");

  const nftIpfsHash2 = await uploadJSONToIPFS(nftMetadata2);
  const nftHash2 = createHash("sha256")
    .update(JSON.stringify(nftMetadata2))
    .digest("hex");

  const nftIpfsHash3 = await uploadJSONToIPFS(nftMetadata3);
  const nftHash3 = createHash("sha256")
    .update(JSON.stringify(nftMetadata3))
    .digest("hex");

  const newCollection = await client.nftClient.createNFTCollection({
    name: "Test NFTs 2025",
    symbol: "TEST",
    isPublicMinting: false,
    mintOpen: true,
    mintFeeRecipient: zeroAddress,
    contractURI: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeiev6gatkl5oqtietqqpg5iocpzcqppwnjnipgwwuas2udxpzo7gxy",
    txOptions: { waitForTransaction: true },
  });

  console.log(
    `New SPG NFT collection created at transaction hash ${newCollection.txHash}`
  );
  console.log(`NFT contract address: ${newCollection.spgNftContract}`);

  // const re = await client.ipAsset.register({
  //   nftContract: "0x6e35d216ea5653F6164059D30891ec5496F75fF4" as Address,
  //   tokenId: 0,
  //   ipMetadata: {
  //     ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash}`,
  //     ipMetadataHash: `0x${ipHash}`,
  //     nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash}`,
  //     nftMetadataHash: `0x${nftHash}`,
  //   },
  //   txOptions: { waitForTransaction: true },
  // });

  // console.log(`IP registered at transaction hash ${re.txHash}`);
  // console.log(`View on the explorer: https://aeneid.explorer.story.foundation/ipa/${re.ipId}`);

  // return

  // 改用单独的mintAndRegisterIp方法，因为它支持spgNftContract参数
  const response = await client.ipAsset.mintAndRegisterIp({
    spgNftContract: newCollection.spgNftContract as Address,
    allowDuplicates: true,
    ipMetadata: {
      ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash}`,
      ipMetadataHash: `0x${ipHash}`,
      nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash}`,
      nftMetadataHash: `0x${nftHash}`,
    },
    txOptions: { waitForTransaction: true },
  });


  console.log(
    `Root IPA created at transaction hash ${response.txHash}, IPA ID: ${response.ipId}`
  );
  console.log(
    `View on the explorer: https://aeneid.explorer.story.foundation/ipa/${response.ipId}`
  );

  const response2 = await client.ipAsset.mintAndRegisterIp({
    spgNftContract: newCollection.spgNftContract as Address,
    allowDuplicates: true,
    ipMetadata: {
      ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash2}`,
      ipMetadataHash: `0x${ipHash}`,
      nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash2}`,
      nftMetadataHash: `0x${nftHash2}`,
    },
    txOptions: { waitForTransaction: true },
  });


  console.log(
    `Root IPA created at transaction hash ${response2.txHash}, IPA ID: ${response2.ipId}`
  );
  console.log(
    `View on the explorer: https://aeneid.explorer.story.foundation/ipa/${response2.ipId}`
  );


  const response3 = await client.ipAsset.mintAndRegisterIp({
    spgNftContract: newCollection.spgNftContract as Address,
    allowDuplicates: true,
    ipMetadata: {
      ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash3}`,
      ipMetadataHash: `0x${ipHash3}`,
      nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash3}`,
      nftMetadataHash: `0x${nftHash3}`,
    },
    txOptions: { waitForTransaction: true },
  });


  console.log(
    `Root IPA created at transaction hash ${response3.txHash}, IPA ID: ${response3.ipId}`
  );
  console.log(
    `View on the explorer: https://aeneid.explorer.story.foundation/ipa/${response3.ipId}`
  );

}

main();
