{"name": "story-demo", "module": "index.ts", "type": "module", "scripts": {"dev": "bun run index.ts", "main": "bun run index_main.ts", "single-image-demo": "bun run single-image-demo.ts", "test-single-image": "bun run test-single-image-licensing.ts"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5.0.0"}, "dependencies": {"@story-protocol/core-sdk": "^1.3.0-rc.2", "pinata-web3": "^0.5.4", "viem": "^2.23.2"}}