import { client } from "./single-image-licensing";
import { SingleImageLicenseManager } from "./single-image-licensing";
import { zeroAddress } from "viem";
import type { Address } from "viem/accounts";

async function demonstrateSingleImageLicensing() {
  console.log("🚀 开始演示单张形象授权功能...\n");

  // 创建单张形象授权管理器
  const licenseManager = new SingleImageLicenseManager(client);

  try {
    // 步骤 1: 注册所有许可证条款类型
    console.log("📋 步骤 1: 注册许可证条款类型");
    console.log("=" .repeat(50));
    await licenseManager.registerAllLicenseTerms();
    console.log("\n");

    // 步骤 2: 创建 NFT 集合（如果需要）
    console.log("🎨 步骤 2: 创建 NFT 集合");
    console.log("=" .repeat(50));
    const newCollection = await client.nftClient.createNFTCollection({
      name: "Single Image Licensing Demo",
      symbol: "SILD",
      isPublicMinting: false,
      mintOpen: true,
      mintFeeRecipient: zeroAddress,
      contractURI: "https://example.com/collection-metadata.json",
      txOptions: { waitForTransaction: true },
    });

    console.log(`✅ NFT 集合创建成功!`);
    console.log(`   合约地址: ${newCollection.spgNftContract}`);
    console.log(`   交易哈希: ${newCollection.txHash}\n`);

    // 步骤 3: 创建第一张图像的 IP 资产（个人使用 + 商业使用）
    console.log("🖼️ 步骤 3: 创建第一张图像 IP 资产");
    console.log("=" .repeat(50));
    const image1Response = await licenseManager.createSingleImageIPWithLicensing(
      {
        title: "数字艺术作品 #001",
        description: "这是一个支持个人使用和商业使用授权的数字艺术作品",
        imageUrl: "https://picsum.photos/id/100/800/600"
      },
      newCollection.spgNftContract as Address,
      ["PERSONAL_USE", "COMMERCIAL_USE"]
    );
    console.log("\n");

    // 步骤 4: 创建第二张图像的 IP 资产（支持衍生作品）
    console.log("🎭 步骤 4: 创建第二张图像 IP 资产（支持衍生作品）");
    console.log("=" .repeat(50));
    const image2Response = await licenseManager.createSingleImageIPWithLicensing(
      {
        title: "创意插画 #002",
        description: "这是一个支持衍生作品创作的创意插画",
        imageUrl: "https://picsum.photos/id/200/800/600"
      },
      newCollection.spgNftContract as Address,
      ["PERSONAL_USE", "DERIVATIVE_WORKS"]
    );
    console.log("\n");

    // 步骤 5: 创建第三张图像的 IP 资产（限量商业许可证）
    console.log("💎 步骤 5: 创建第三张图像 IP 资产（限量商业许可证）");
    console.log("=" .repeat(50));
    const image3Response = await licenseManager.createSingleImageIPWithLicensing(
      {
        title: "限量版艺术品 #003",
        description: "这是一个限量商业授权的高价值艺术品",
        imageUrl: "https://picsum.photos/id/300/800/600"
      },
      newCollection.spgNftContract as Address,
      ["PERSONAL_USE", "LIMITED_COMMERCIAL"]
    );
    console.log("\n");

    // 步骤 6: 为第一张图像添加额外的许可证类型
    console.log("➕ 步骤 6: 为第一张图像添加衍生作品许可证");
    console.log("=" .repeat(50));
    if (image1Response.ipId) {
      await licenseManager.attachLicenseToExistingIP(
        image1Response.ipId,
        "DERIVATIVE_WORKS"
      );
    }
    console.log("\n");

    // 步骤 7: 演示许可证代币铸造
    console.log("🪙 步骤 7: 铸造许可证代币");
    console.log("=" .repeat(50));
    
    if (image1Response.ipId) {
      // 铸造个人使用许可证代币（免费）
      console.log("铸造个人使用许可证代币...");
      await licenseManager.mintLicenseToken(
        image1Response.ipId,
        "PERSONAL_USE",
        1
      );
      console.log("");

      // 铸造商业使用许可证代币（付费）
      console.log("铸造商业使用许可证代币...");
      await licenseManager.mintLicenseToken(
        image1Response.ipId,
        "COMMERCIAL_USE",
        1
      );
      console.log("");
    }

    // 步骤 8: 显示所有已注册的许可证条款
    console.log("📊 步骤 8: 已注册的许可证条款总览");
    console.log("=" .repeat(50));
    const registeredTerms = licenseManager.listRegisteredLicenseTerms();
    registeredTerms.forEach((id, type) => {
      console.log(`${type}: ${id}`);
    });
    console.log("\n");

    // 步骤 9: 显示创建的 IP 资产总览
    console.log("🎯 步骤 9: 创建的 IP 资产总览");
    console.log("=" .repeat(50));
    console.log(`图像 1 - 数字艺术作品 #001:`);
    console.log(`  IP ID: ${image1Response.ipId}`);
    console.log(`  浏览器: https://aeneid.explorer.story.foundation/ipa/${image1Response.ipId}`);
    console.log(`  支持的许可证: 个人使用, 商业使用, 衍生作品\n`);

    console.log(`图像 2 - 创意插画 #002:`);
    console.log(`  IP ID: ${image2Response.ipId}`);
    console.log(`  浏览器: https://aeneid.explorer.story.foundation/ipa/${image2Response.ipId}`);
    console.log(`  支持的许可证: 个人使用, 衍生作品\n`);

    console.log(`图像 3 - 限量版艺术品 #003:`);
    console.log(`  IP ID: ${image3Response.ipId}`);
    console.log(`  浏览器: https://aeneid.explorer.story.foundation/ipa/${image3Response.ipId}`);
    console.log(`  支持的许可证: 个人使用, 限量商业\n`);

    console.log("🎉 单张形象授权功能演示完成!");
    console.log("\n💡 现在用户可以:");
    console.log("   • 免费获取个人使用许可证");
    console.log("   • 付费购买商业使用许可证");
    console.log("   • 购买衍生作品创作许可证");
    console.log("   • 购买限量商业许可证（有时间和收益限制）");

  } catch (error) {
    console.error("❌ 演示过程中发生错误:", error);
  }
}

// 运行演示
demonstrateSingleImageLicensing();
